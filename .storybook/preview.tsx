import { Global } from '@emotion/react';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import React from 'react';
import { INITIAL_VIEWPORTS, MINIMAL_VIEWPORTS } from 'storybook/viewport';

import Layout from '../src/components/global/Layout/Layout';
import { CartShippingContextProvider } from '../src/components/modules/Cart/CartShipping.context';
import { CartSummaryContextProvider } from '../src/components/modules/Cart/CartSummary.context';
import { TabContextProvider } from '../src/components/modules/Cart/CartTab.context';
import { CartUserActionContextProvider } from '../src/components/modules/Cart/CartUserAction.context';
import { PaymentDataContextProvider } from '../src/components/modules/PaymentData/PaymentData.context';
import { SearchContextProvider } from '../src/components/modules/Search/Search.context';
import { SearchModalContextProvider } from '../src/components/modules/Search/SearchModal.context';
import { SelectedInstallerContextProvider } from '../src/components/modules/SelectedInstaller/SelectedInstaller.context';
import { AuthViaStatusContextProvider } from '../src/components/pages/CheckoutPage/Payment/AuthVia/AuthViaStatus.context';
import { BraintreeContextProvider } from '../src/components/pages/CheckoutPage/Payment/Braintree/Braintree.context';
import { ComponentErrorBoundaryCounterContextProvider } from '../src/context/ComponentErrorBoundaryCounter.context';
import { ContactEmailModalContextProvider } from '../src/context/ContactEmailModal.context';
import { FooterContextProvider } from '../src/context/Footer.context';
import { GlobalsContextProvider } from '../src/context/Globals.context';
import { GlobalToastContextProvider } from '../src/context/GlobalToast.context';
import { GuidedQuestionsModalProvider } from '../src/context/GuidedQuestionsModal.context';
import { ModalContextProvider } from '../src/context/Modal.context';
import { NavContextProvider } from '../src/context/Nav.context';
import { RouterContextProvider } from '../src/context/Router.context';
import { SimpleScoreCardModalContextProvider } from '../src/context/SimpleScoreCardModal.context';
import { SiteGlobalsContextProvider } from '../src/context/SiteGlobals.context';
import { SiteMenuContextProvider } from '../src/context/SiteMenu.context';
import { SiteNotificationsContextProvider } from '../src/context/SiteNotifications.context';
import AllThirdPartyScriptsContext from '../src/context/ThirdPartyScriptsContext/AllThirdPartyScriptsContext';
import { UserLocationContextProvider } from '../src/context/UserLocation.context';
import { UserPersonalizationContextProvider } from '../src/context/UserPersonalization.context';
import { UserZipManualEntryModalProvider } from '../src/context/UserZipManualEntryModal.context';
import { WidgetConfigProvider } from '../src/context/WidgetConfig.context';
import { global } from '../src/styles/document/global.styles';

const mockSiteGlobalContextValue = {
  customerServiceEnabled: false,
  priceDisplayInAddtoCart: true,
  siteTheme: '',
  customerServiceNumber: {
    display: '(888) 410-0604',
    value: '18884100604',
  },
};
const mockSiteMenuContextValue = {
  siteMenuBrowseList: [
    {
      title: 'Brand',
      siteMenuBrowseGroupList: [
        {
          header: null,
          items: [
            {
              label: 'Pirelli',
              link: {
                href: '/brands/pirelli-tires',
                isExternal: false,
              },
              flair: {
                type: 'string',
                value: 'Best Seller',
              },
              icon: {
                altText: 'Pirelli',
                height: 22,
                src: 'https://images.simpletire.com/image/upload/v1593208962/manf-logos/72b.svg',
                type: 'SiteImage',
                width: 100,
              },
            },
            {
              label: 'Nitto',
              link: {
                href: '/brands/nitto-tires',
                isExternal: false,
              },
              flair: null,
              icon: {
                altText: 'Nitto',
                height: 9,
                src: 'https://images.simpletire.com/image/upload/v1597238228/manf-logos/68b_new.svg',
                type: 'SiteImage',
                width: 140,
              },
            },
          ],
          more: {
            label: 'See all 20 categories',
            link: {
              href: '/categories',
              isExternal: false,
            },
          },
        },
      ],
      info: null,
    },
  ],
  siteMenuLearn: {
    list: [
      {
        label: 'Free shipping',
        link: {
          href: '/free-shipping',
          isExternal: false,
        },
      },
      {
        label: 'Tire buying guide',
        link: {
          href: '/tire-buying-guide ',
          isExternal: false,
        },
      },
      {
        label: 'FAQs',
        link: {
          href: '/faqs',
          isExternal: false,
        },
      },
    ],
  },
};

function GlobalStyles() {
  return <Global styles={global} />;
}

const withGlobal = (Story) => (
  <GlobalsContextProvider value={{}}>
    <SiteGlobalsContextProvider value={mockSiteGlobalContextValue}>
      <ComponentErrorBoundaryCounterContextProvider>
        <AllThirdPartyScriptsContext>
          <NavContextProvider>
            <SiteMenuContextProvider value={mockSiteMenuContextValue}>
              <UserPersonalizationContextProvider>
                <UserLocationContextProvider>
                  <FooterContextProvider>
                    <GlobalToastContextProvider>
                      <SearchContextProvider>
                        <SearchModalContextProvider>
                          <TabContextProvider>
                            <CartSummaryContextProvider>
                              <CartShippingContextProvider>
                                <CartUserActionContextProvider>
                                  <AuthViaStatusContextProvider>
                                    <BraintreeContextProvider>
                                      <PaymentDataContextProvider>
                                        <RouterContextProvider>
                                          <SiteNotificationsContextProvider>
                                            <ModalContextProvider>
                                              <ContactEmailModalContextProvider>
                                                <SimpleScoreCardModalContextProvider>
                                                  <UserZipManualEntryModalProvider>
                                                    <WidgetConfigProvider>
                                                      <SelectedInstallerContextProvider>
                                                        <GuidedQuestionsModalProvider>
                                                          <Layout>
                                                            {Story()}
                                                          </Layout>
                                                        </GuidedQuestionsModalProvider>
                                                      </SelectedInstallerContextProvider>
                                                    </WidgetConfigProvider>
                                                  </UserZipManualEntryModalProvider>
                                                </SimpleScoreCardModalContextProvider>
                                              </ContactEmailModalContextProvider>
                                            </ModalContextProvider>
                                          </SiteNotificationsContextProvider>
                                        </RouterContextProvider>
                                      </PaymentDataContextProvider>
                                    </BraintreeContextProvider>
                                  </AuthViaStatusContextProvider>
                                </CartUserActionContextProvider>
                              </CartShippingContextProvider>
                            </CartSummaryContextProvider>
                          </TabContextProvider>
                        </SearchModalContextProvider>
                      </SearchContextProvider>
                    </GlobalToastContextProvider>
                  </FooterContextProvider>
                </UserLocationContextProvider>
              </UserPersonalizationContextProvider>
            </SiteMenuContextProvider>
          </NavContextProvider>
        </AllThirdPartyScriptsContext>
      </ComponentErrorBoundaryCounterContextProvider>
    </SiteGlobalsContextProvider>
  </GlobalsContextProvider>
);

const customViewports = {
  large: {
    name: 'desktop',
    styles: {
      height: 'calc(100% - 20px)',
      width: '976px',
    },
    type: 'desktop',
  },
  xlarge: {
    name: 'desktop-xlarge',
    styles: {
      height: 'calc(100% - 20px)',
      width: '1200px',
    },
    type: 'desktop',
  },
};

const parameters = {
  a11y: {
    element: '#storybook-root',
    config: {},
    options: {},
  },
  backgrounds: {
    default: 'white',
    values: [
      {
        name: 'white',
        value: '#fffff',
      },
      {
        name: 'black',
        value: '#181818',
      },
      {
        name: 'orange',
        value: '#FE5F10',
      },
    ],
  },
  docs: {
    codePanel: true,
  },
  nextjs: {
    appDirectory: true,
  },
  viewport: {
    viewports: {
      ...INITIAL_VIEWPORTS,
      ...MINIMAL_VIEWPORTS,
      ...customViewports,
    },
    defaultViewport: 'xlarge',
  },
};

const decorators = [
  withThemeFromJSXProvider({
    GlobalStyles, // Adds your GlobalStyles component to all stories
  }),
  withGlobal,
];

const preview = {
  decorators,
  parameters,
};

export default preview;
