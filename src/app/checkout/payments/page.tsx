import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';

import { cookieConfig } from '~/components/modules/Cart/CartSummary.constants';
import { PaymentOptionsContainerProps } from '~/components/pages/CheckoutPage/Payments/PaymentOptions.types';
import PaymentOptionsContainer from '~/components/pages/CheckoutPage/Payments/PaymentOptionsContainer';
import { AppRouteCheckoutPaymentsPageParams } from '~/data/AppRoutePageParams';
import { SiteCartAppointmentApiResponse } from '~/data/models/SiteCartAppointmentResponse';
import { SiteCartBillingApiResponse } from '~/data/models/SiteCartBillingResponse';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import {
  ShippingType,
  SiteCartShippingApiResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { backendGetCartAppointment } from '~/lib/backend/checkout/cart-appointment';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { backendGetCartSummary } from '~/lib/backend/checkout/cart-summary';
import { COOKIES } from '~/lib/constants/cookies';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { AsyncResponse } from '~/lib/fetch/index.types';
import { getUserIpForAppRoute } from '~/lib/utils/ip';
import { getStringifiedParams } from '~/lib/utils/routes';

async function CheckoutPaymentsPage(
  appRoutePageParams: AppRouteCheckoutPaymentsPageParams,
) {
  const pageParams = await appRoutePageParams.searchParams;
  const queryParams = getStringifiedParams(pageParams);
  const { cartId, widgetSource, cartid, widgetType, widgetSourceId } =
    queryParams;

  const [cookieStore, headersList] = await Promise.all([cookies(), headers()]);
  const userIp = getUserIpForAppRoute(headersList);
  let storedCartId;
  let setCookieForWidget = async () => {
    'use server';
  };

  if (
    widgetSource === 'pirelli' ||
    widgetType === 'installer-widget' ||
    widgetType === 'affiliate-widget' ||
    widgetType === 'manufacturer-widget'
  ) {
    storedCartId = cartId;
    setCookieForWidget = async () => {
      'use server';

      cookieStore.set(COOKIES.CART_ID, String(cartId), cookieConfig);
    };
  } else {
    storedCartId =
      cartId ?? cartid ?? cookieStore.get(COOKIES.CART_ID)?.value ?? '';
  }

  if (!storedCartId) {
    // FND-1606: redirect to home page directly if cartId is not found.
    redirect('/');
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query: queryParams,
  });

  let cartShippingResponse: AsyncResponse<SiteCartShippingApiResponse> | null =
    null;

  let cartAppointment: AsyncResponse<SiteCartAppointmentApiResponse> | null =
    null;

  let cartBillingResponse: AsyncResponse<SiteCartBillingApiResponse> | null =
    null;

  const cartSummaryResponse: AsyncResponse<SiteCartSummaryResponse> =
    await backendGetCartSummary(
      {
        id: storedCartId,
        query: {
          ...(widgetSource && widgetSourceId
            ? { widgetSource, widgetSourceId }
            : {}),
        },
      },
      extraQueryParams,
    );

  if (!cartSummaryResponse.isSuccess) {
    redirect('/');
  }

  [cartShippingResponse, cartBillingResponse] = await Promise.all([
    backendGetCartShipping({ cartId: storedCartId }, extraQueryParams),
    backendGetCartBilling({ cartId: storedCartId }, extraQueryParams),
  ]);

  if (
    cartShippingResponse.isSuccess &&
    cartShippingResponse.data.siteCartShippingResponse.shippingOption ===
      ShippingType.INSTALLER
  ) {
    cartAppointment = await backendGetCartAppointment(
      {
        cartId: storedCartId,
      },
      extraQueryParams,
    );
  }

  const cartBilling =
    cartBillingResponse &&
    cartBillingResponse.isSuccess &&
    cartBillingResponse.data
      ? cartBillingResponse.data.siteCartBillingResponse.cartBilling
      : null;

  const initialBillingInfo = {
    addressLine1: cartBilling?.addressLine1 ?? '',
    addressLine2: cartBilling?.addressLine2 ?? '',
    city: cartBilling?.city ?? '',
    firstName: cartBilling?.firstName ?? '',
    lastName: cartBilling?.lastName ?? '',
    phone: cartBilling?.phone ?? '',
    phoneType: cartBilling?.phoneType ?? PHONE_TYPE.MOBILE,
    poNumber: cartBilling?.poNumber ?? '',
    state: cartBilling?.state ?? '',
    zip: cartBilling?.zip ?? '',
  };

  const props: PaymentOptionsContainerProps = {
    initialBillingInfo,
    setCookieForWidget,
    siteCartAppointment:
      cartAppointment && cartAppointment.isSuccess
        ? cartAppointment.data.SiteCartAppointment
        : null,
    siteCartShipping:
      cartShippingResponse && cartShippingResponse.isSuccess
        ? cartShippingResponse.data.siteCartShippingResponse
        : null,
    siteCartSummary: cartSummaryResponse.data,
    userIp: userIp || null,
  };

  return <PaymentOptionsContainer {...props} />;
}

export default CheckoutPaymentsPage;
