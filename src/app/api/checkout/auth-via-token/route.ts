import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendPostAuthViaClientToken,
  backendPutAuthViaClientToken,
} from '~/lib/backend/checkout/auth-via-token';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';

export async function POST() {
  await backendBootstrap();
  const extraQueryParams = await generateExtraQueryParams({});

  const res = await backendPostAuthViaClientToken(extraQueryParams);

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (res.error) {
    return new Response(null, {
      status: res.error.statusCode,
    });
  }

  return new Response(null, { status: 500 });
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const extraQueryParams = await generateExtraQueryParams({});
  const body = await request.json();

  const res = await backendPutAuthViaClientToken(
    { input: body },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (res.error) {
    return new Response(null, {
      status: res.error.statusCode,
    });
  }

  return new Response(null, { status: 500 });
}
