'use client';

import { type ReactNode } from 'react';

import { InstallationShopDetailsModalContextProvider } from '~/components/global/InstallationShopDetails/InstallationShopDetailsModal.context';
import { CartShippingContextProvider } from '~/components/modules/Cart/CartShipping.context';
import { CartSummaryContextProvider } from '~/components/modules/Cart/CartSummary.context';
import { CartUserActionContextProvider } from '~/components/modules/Cart/CartUserAction.context';
import { SearchContextProvider } from '~/components/modules/Search/Search.context';
import { SearchModalContextProvider } from '~/components/modules/Search/SearchModal.context';
import { SelectedInstallerContextProvider } from '~/components/modules/SelectedInstaller/SelectedInstaller.context';
import { TireSnapModalContextProvider } from '~/components/modules/TireSnap/TireSnapModal.context';
import { AuthViaStatusContextProvider } from '~/components/pages/CheckoutPage/Payment/AuthVia/AuthViaStatus.context';
import { type SiteGlobals } from '~/data/models/SiteGlobals';
import { type SiteMenuContextProps } from '~/data/models/SiteMenu';
import { type SiteNotificationList } from '~/data/models/SiteNotificationsList';

import { ComponentErrorBoundaryCounterContextProvider } from './ComponentErrorBoundaryCounter.context';
import { ContactEmailModalContextProvider } from './ContactEmailModal.context';
import { FooterContextProvider } from './Footer.context';
import { GlobalToastContextProvider } from './GlobalToast.context';
import { GuidedQuestionsModalProvider } from './GuidedQuestionsModal.context';
import { ModalContextProvider } from './Modal.context';
import { NavContextProvider } from './Nav.context';
import PaymentRelatedContext from './PaymentRelatedContext/PaymentRelatedContext';
import { RecentlyViewedTiresProvider } from './RecentlyViewedTires.context';
import { RouterContextProvider } from './Router.context';
import { SimpleScoreCardModalContextProvider } from './SimpleScoreCardModal.context';
import { SiteGlobalsContextProvider } from './SiteGlobals.context';
import { SiteMenuContextProvider } from './SiteMenu.context';
import { SiteNotificationsContextProvider } from './SiteNotifications.context';
import AllThirdPartyScriptsContext from './ThirdPartyScriptsContext/AllThirdPartyScriptsContext';
import { UserLocationContextProvider } from './UserLocation.context';
import { UserPersonalizationContextProvider } from './UserPersonalization.context';
import { UserZipManualEntryModalProvider } from './UserZipManualEntryModal.context';

interface Props {
  children: ReactNode;
  siteGlobalsContextValue?: SiteGlobals;
  siteMenuContextValue?: SiteMenuContextProps;
  siteNotificationContextValue?: SiteNotificationList;
}

// Container to wrap _app.tsx in context providers.
// Not all providers need to go here; only ones used throughout the app
function AppProviders({
  children,
  siteGlobalsContextValue,
  siteMenuContextValue,
  siteNotificationContextValue,
}: Props) {
  return (
    <SiteGlobalsContextProvider value={siteGlobalsContextValue}>
      <ComponentErrorBoundaryCounterContextProvider>
        <AllThirdPartyScriptsContext>
          <NavContextProvider>
            <SiteMenuContextProvider value={siteMenuContextValue}>
              <UserPersonalizationContextProvider>
                <UserLocationContextProvider>
                  <FooterContextProvider>
                    <SearchContextProvider>
                      <SearchModalContextProvider>
                        <CartSummaryContextProvider>
                          <CartShippingContextProvider>
                            <CartUserActionContextProvider>
                              <RouterContextProvider>
                                <AuthViaStatusContextProvider>
                                  <PaymentRelatedContext>
                                    <TireSnapModalContextProvider>
                                      <GlobalToastContextProvider>
                                        <InstallationShopDetailsModalContextProvider>
                                          <SiteNotificationsContextProvider
                                            value={siteNotificationContextValue}
                                          >
                                            <ModalContextProvider>
                                              <ContactEmailModalContextProvider>
                                                <RecentlyViewedTiresProvider>
                                                  <SimpleScoreCardModalContextProvider>
                                                    <UserZipManualEntryModalProvider>
                                                      <SelectedInstallerContextProvider>
                                                        <GuidedQuestionsModalProvider>
                                                          {children}
                                                        </GuidedQuestionsModalProvider>
                                                      </SelectedInstallerContextProvider>
                                                    </UserZipManualEntryModalProvider>
                                                  </SimpleScoreCardModalContextProvider>
                                                </RecentlyViewedTiresProvider>
                                              </ContactEmailModalContextProvider>
                                            </ModalContextProvider>
                                          </SiteNotificationsContextProvider>
                                        </InstallationShopDetailsModalContextProvider>
                                      </GlobalToastContextProvider>
                                    </TireSnapModalContextProvider>
                                  </PaymentRelatedContext>
                                </AuthViaStatusContextProvider>
                              </RouterContextProvider>
                            </CartUserActionContextProvider>
                          </CartShippingContextProvider>
                        </CartSummaryContextProvider>
                      </SearchModalContextProvider>
                    </SearchContextProvider>
                  </FooterContextProvider>
                </UserLocationContextProvider>
              </UserPersonalizationContextProvider>
            </SiteMenuContextProvider>
          </NavContextProvider>
        </AllThirdPartyScriptsContext>
      </ComponentErrorBoundaryCounterContextProvider>
    </SiteGlobalsContextProvider>
  );
}

export default AppProviders;
