import { NextApiRequest, NextApiResponse } from 'next';

import { defaultGlobals } from '~/context/SiteGlobals.context';
import { SiteGlobals } from '~/data/models/SiteGlobals';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetSiteGlobals } from '~/lib/backend/site';
import { COOKIES } from '~/lib/constants/cookies';

const globalsApi = async (
  request: NextApiRequest,
  response: NextApiResponse<{
    siteGlobals: SiteGlobals;
  }>,
) => {
  backendBootstrap({ request });

  const cookies = request.cookies;
  const otsAccountType = process.env.NEXT_PUBLIC_OTS_ACCOUNT_TYPE;
  const otsAccountTypeCompany = cookies[COOKIES.OTS_ACCOUNT_TYPE_COMPANY];
  const otsWidgetId = cookies[COOKIES.OTS_WIDGET_ID];
  let userType = cookies[COOKIES.SSO_ACCOUNT_TYPE];
  userType = userType ? userType.toLowerCase() : 'steer';

  const res =
    process.env.IS_OTS === '1'
      ? otsAccountTypeCompany && otsWidgetId && otsAccountType
        ? await backendGetSiteGlobals(otsAccountType, request)
        : null
      : await backendGetSiteGlobals(userType, request);

  if (!res) {
    response.json({ siteGlobals: defaultGlobals });
    return;
  }

  if (res.isSuccess) {
    response.json(res.data);
    return;
  }

  response.status(res.error.statusCode).end();
};

export default globalsApi;
