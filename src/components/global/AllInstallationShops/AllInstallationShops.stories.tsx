import { useState } from 'react';
import { action } from 'storybook/actions';

import Link from '~/components/global/Link/Link';
import Modal from '~/components/global/Modal/Modal';
import { CartShippingContextProvider } from '~/components/modules/Cart/CartShipping.context';
import { CartSummaryContextProvider } from '~/components/modules/Cart/CartSummary.context';
import { CartUserActionContextProvider } from '~/components/modules/Cart/CartUserAction.context';
import { SearchContextProvider } from '~/components/modules/Search/Search.context';
import { SearchModalContextProvider } from '~/components/modules/Search/SearchModal.context';
import { mockCartShippingResponse } from '~/components/pages/CheckoutPage/ShippingConfirmation/ShippingConfirmation.data';
import {
  siteFaqMock,
  siteProductMock,
  siteProductReviewsMock,
} from '~/components/pages/ProductDetail/mappers/ProductDetail.mock';
import { ProductDetailDataContextProvider } from '~/components/pages/ProductDetail/ProductDetailData.context';
import { UserLocation } from '~/data/models/UserLocation';
import { MQ } from '~/lib/constants/breakpoints';
import { THEME } from '~/lib/constants/theme';
import { Z_INDEX } from '~/lib/constants/zindex';

import AllInstallationShops from './AllInstallationShops';
import { promotions, shopsMockData } from './AllInstallationShops.mock';

export default {
  component: AllInstallationShops,
  title: 'Global/AllInstallationShops',
};

const ALL_INSTALLATION_SHOPS_MODAL_OVERLAY_ZINDEX = Z_INDEX.MODAL - 10;

const zipCode = '33141';

interface AllInstallationShopsDefaultArgs {
  isLoadingShopData: boolean;
  userLocation?: UserLocation;
  withBrandPromotion: boolean;
}

function AllInstallationShopsDefaultRender({
  isLoadingShopData,
  userLocation,
  withBrandPromotion,
}: AllInstallationShopsDefaultArgs) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const toggleModal = () => {
    setIsOpen(!isOpen);
  };
  const toggleLocationModal = () => {
    alert('cdd');
    setIsLocationModalOpen(!isLocationModalOpen);
  };
  const [selectedInstaller, setSelectedInstaller] = useState(
    shopsMockData[0].id,
  );
  const [isLoadingInstallerSchedule, setIsLoadingInstallerSchedule] =
    useState(false);

  function handleSetSelectedInstaller(value: string) {
    if (value === '') {
      setSelectedInstaller(value);
    } else {
      setIsLoadingInstallerSchedule(true);
      setSelectedInstaller(value);
      setTimeout(() => {
        setIsLoadingInstallerSchedule(false);
      }, 3000);
    }
  }

  return (
    <SearchContextProvider>
      <SearchModalContextProvider>
        <CartSummaryContextProvider>
          <CartShippingContextProvider value={mockCartShippingResponse}>
            <CartUserActionContextProvider>
              <ProductDetailDataContextProvider
                serverData={{
                  siteFaqs: siteFaqMock,
                  siteProduct: siteProductMock,
                  siteProductReviews: siteProductReviewsMock,
                  userIp: '',
                }}
              >
                <div>
                  <Link as="button" theme={THEME.LIGHT} onClick={toggleModal}>
                    See all shops nearaaa {zipCode}
                  </Link>
                  <Modal
                    contentLabel="All Shops Modal"
                    hasCloseButton
                    onClose={toggleModal}
                    isHalfscreen
                    isOpen={isOpen}
                    customContainerStyles={{
                      padding: 0,

                      [MQ.L]: { minWidth: 768 },
                    }}
                    overlayZIndex={ALL_INSTALLATION_SHOPS_MODAL_OVERLAY_ZINDEX}
                  >
                    <AllInstallationShops
                      brandPromotionList={withBrandPromotion ? promotions : []}
                      shops={shopsMockData}
                      selectedInstaller={selectedInstaller}
                      handleSelectedInstallerChange={handleSetSelectedInstaller}
                      userLocation={userLocation}
                      openStaticModal={action('openStaticModal')}
                      openDynamicModal={action('openDynamicModal')}
                      isLoadingShopData={isLoadingShopData}
                      isLocationModalOpen={isLocationModalOpen}
                      toggleLocationModal={toggleLocationModal}
                      isLoadingInstallerSchedule={isLoadingInstallerSchedule}
                    />
                  </Modal>
                </div>
              </ProductDetailDataContextProvider>
            </CartUserActionContextProvider>
          </CartShippingContextProvider>
        </CartSummaryContextProvider>
      </SearchModalContextProvider>
    </SearchContextProvider>
  );
}

export const AllInstallationShopsDefault = {
  args: {
    isLoadingShopData: false,
    userLocation: {
      cityName: 'Miami Beach',
      stateAbbr: 'FL',
      zip: zipCode,
      region: null,
    },
    withBrandPromotion: true,
  },
  argTypes: {
    alwaysShowMap: { table: { disable: true } },
    brandPromotionList: { table: { disable: true } },
    customContainerStyle: { table: { disable: true } },
    customMapStyles: { table: { disable: true } },
    customSelectText: { table: { disable: true } },
    customShopsStyles: { table: { disable: true } },
    displayShippingPreferenceLink: { table: { disable: true } },
    error: { table: { disable: true } },
    hideExtraDetails: { table: { disable: true } },
    hideMapButton: { table: { disable: true } },
    hideSelectButton: { table: { disable: true } },
    hideStaticMap: { table: { disable: true } },
    hideTitle: { table: { disable: true } },
    isCarousel: { table: { disable: true } },
    isLocationModalOpen: { table: { disable: true } },
    isOnAllShopsModal: { table: { disable: true } },
    isOnUrbanAreaPage: { table: { disable: true } },
    isTireShopsPage: { table: { disable: true } },
    loadLocalShop: { table: { disable: true } },
    mapDataComponent: { table: { disable: true } },
    onClickAppointment: { table: { disable: true } },
    onConfirm: { table: { disable: true } },
    onShippingPrefenceLinkClick: { table: { disable: true } },
    onTabChange: { table: { disable: true } },
    openStaticModal: { table: { disable: true } },
    selectedTime: { table: { disable: true } },
    shops: { table: { disable: true } },
    showInfo: { table: { disable: true } },
    showTwoRows: { table: { disable: true } },
    toggleLocationModal: { table: { disable: true } },
    unclickableLocation: { table: { disable: true } },
    visibleShopsNumber: { table: { disable: true } },
  },
  render: (args: AllInstallationShopsDefaultArgs) => {
    return <AllInstallationShopsDefaultRender {...args} />;
  },
  parameters: {
    nextjs: {
      router: {
        basePath: '/',
      },
    },
  },
};
