/**
 * @NOTE: this context is only for handling the concerns of cart summary apis
 */
import lscache from 'lscache';
import { usePathname } from 'next/navigation';
import { parseCookies, setCookie } from 'nookies';
import {
  Dispatch,
  memo,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { INSTALL_TAB } from '~/components/global/AllInstallationShops/AllInstallationShops.constants';
import { checkIfRoadHazardIsApplicable } from '~/components/pages/CheckoutPage/checkout.util';
import { ShipToMeFormValues } from '~/components/pages/CheckoutPage/Shipping/ShippingForNonInstallableTires/ShipToMe/ShipToMe.types';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartAppointmentResponse } from '~/data/models/SiteCartAppointmentResponse';
import { SiteCartCouponItem } from '~/data/models/SiteCartCouponItem';
import {
  SHIPPINGSERIVCES,
  ShippingType,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import {
  IDMEVerifiedStatus,
  SiteCartSummaryRequest,
} from '~/data/models/SiteCartSummaryRequest';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { SiteInstallerItem } from '~/data/models/SiteInstallerItem';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import useBreakpoints from '~/hooks/useBreakpoints';
import useEffectOnlyOnce from '~/hooks/useEffectOnlyOnce';
import useRouter from '~/hooks/useRouter';
import useWidgetSource from '~/hooks/useWigetSource';
import {
  apiCreateSiteCartSummary,
  apiGetSiteCartSummary,
  apiUpdateSiteCartSummary,
} from '~/lib/api/checkout/cart-summary';
import { apiGetSiteInstallers } from '~/lib/api/installers';
import { COOKIES } from '~/lib/constants/cookies';
import {
  LOCAL_STORAGE,
  PROPERTIES as localProperties,
} from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { PROPERTIES, SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import { SSO_COOKIE_CONSTANTS, USER_TYPE } from '~/lib/constants/sso';
import { eventEmitters } from '~/lib/events/emitters';
import { AsyncResponse } from '~/lib/fetch/index.types';
import GA from '~/lib/helpers/analytics';
import { getSearchParams } from '~/lib/helpers/app-routes/search-params';
import logger from '~/lib/helpers/logger';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import {
  mapToCartObject,
  mapToProductData,
} from '~/lib/helpers/rudderstack/transformer';
import { ExtendedEventProperties } from '~/lib/helpers/rudderstack/types';
import { loStorage, seStorage } from '~/lib/utils/browser-storage';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';
import { isOTSDeployment, isSimpleShopDeployment } from '~/lib/utils/deploy';
import { isCheckoutPagePath } from '~/lib/utils/routes';

import { SiteInstallerSourceEnum } from '../PDP/Installers/Installers.types';
import { cookieConfig, cookieConfigKings } from './CartSummary.constants';
import { clearCartCookies } from './CartSummary.utils';
import { PromoListItem } from './CartSummaryModal/DealsAndRebatsInCart/PromoCard.types';
import { ConfirmAppointmentProps } from './CartSummaryModal/ShippingService/ShippingService.types';

export interface TriggerType {
  flag: number;
  removeShippingOnly: boolean;
}
export interface CartSummaryContextProps {
  applyPromoCode: (couponCode: string) => void;
  cartId: string;
  cartShipping?: SiteCartShippingResponse;
  cartSummary: SiteCartSummary | null;
  createCartSummary: (
    siteCartSummaryRequest: SiteCartSummaryRequest,
  ) => Promise<AsyncResponse<SiteCartSummaryResponse>>;
  currentOrderCancelled?: boolean;
  customerId: string;
  displayRoadsideAssistance: boolean;
  displayShippingInfo: boolean;
  email: string;
  getSiteCartSummary: () => void;
  getSiteInstallersForMobileOptions: (
    cartSummary: SiteCartSummary | null,
    cartId: string,
  ) => Promise<{
    installerList: SiteInstallerItem[];
    mobileInstallerList: SiteInstallerItem[];
  }>;
  handleCheckout: (input: number) => void;
  hasDefaultSelectMobileInstall: boolean;
  hasErrorFindShop: boolean;
  hasMobileInstall: boolean;
  isAllShopModalOpen: boolean;
  isAppointmentModalOpen: boolean;
  isCartSummaryLoading?: boolean;
  isCartSummaryModalOpen: boolean;
  isHome: boolean;
  isInstallConfirmOpen: boolean;
  isInstaller: boolean;
  isLoading: boolean;
  isOpenRoadHazardDealModal: boolean;
  isOpenTimeChangeModalOnCheckout: boolean;
  isProductAddedInCart: boolean;
  isRemovingRoadHazard: boolean;
  isRoadHazardCheckboxVisible: boolean;
  isRoadHazardChecked?: boolean;
  isShippingPage: boolean;
  isShowingRemoveInstallationAlert: boolean;
  isUpdatingRoadHazard: boolean;
  lastPaymentTypeBeforePaypal: PAYMENT_OPTIONS;
  paymentType: PAYMENT_OPTIONS;
  removeInstallerFromShippingPage: boolean;
  removePromoCode: (couponCode: string, coupon?: SiteCartCouponItem) => void;
  selectedTab: string;
  setCartId: (value: string) => void;
  setCartShipping: (value: SiteCartShippingResponse | undefined) => void;
  setCartSummary: Dispatch<SetStateAction<SiteCartSummary | null>>;
  setCurrentOrderCancelled: (currentOrderCancelled: boolean) => void;
  setCurrentProductId: (productId?: number | undefined) => void;
  setCustomerId: (customerId: string) => void;
  setEmail: (email: string) => void;
  setHasDefaultSelectMobileInstall: (value: boolean) => void;
  setHasErrorFindShop: (value: boolean) => void;
  setHasMobileInstall: (value: boolean) => void;
  setIsAllShopModalOpen: Dispatch<SetStateAction<boolean>>;
  setIsAppointmentModalOpen: Dispatch<SetStateAction<boolean>>;
  setIsCartSummaryModalOpen: (value: boolean) => void;
  setIsHome: (value: boolean) => void;
  setIsInstallConfirmOpen: (isInstallConfirmOpen: boolean) => void;
  setIsInstaller: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
  setIsOpenRoadHazardDealModal: (value: boolean) => void;
  setIsOpenTimeChangeModalOnCheckout: (value: boolean) => void;
  setIsRoadHazardChecked: (value: boolean) => void;
  setIsShowingRemoveInstallationAlert: (value: boolean) => void;
  setLastPaymentTypeBeforePaypal: (value: PAYMENT_OPTIONS) => void;
  setPaymentType: (value: PAYMENT_OPTIONS) => void;
  setSelectedTab: (value: string) => void;
  setShipToMeForm: Dispatch<SetStateAction<ShipToMeFormValues | undefined>>;
  setShopCardValues: (values: ConfirmAppointmentProps | undefined) => void;
  setShouldRemoveInstallerFromShipping: (value: boolean) => void;
  setShowInstallerTabs: Dispatch<SetStateAction<boolean>>;
  setTriggerCartCleanup: (value: TriggerType) => void;
  shipToMeForm?: ShipToMeFormValues;
  shopCardValues?: ConfirmAppointmentProps;
  showInstallerTabs?: boolean;
  triggerCartCleanup: TriggerType;
  updateAndFetchCartSummary: () => void;
  updateCartSummary: (
    input: SiteCartSummaryRequest,
    signal?: AbortSignal,
    forceCall?: boolean,
  ) => Promise<AsyncResponse<SiteCartSummaryResponse> | void>;
  updateIDMeVerifyStatus: (
    status: IDMEVerifiedStatus,
  ) => Promise<AsyncResponse<SiteCartSummaryResponse> | void>;
  updateRoadHazard: (value?: boolean) => void;
  updateTireQuantity: ({
    productId,
    quantity,
  }: {
    productId: number;
    quantity: number;
  }) => Promise<void>;
}

interface ContextProps {
  children: ReactNode;
}

const CartSummaryContext = createContext<CartSummaryContextProps>();

function useContextSetup() {
  const { updateLocation, userType } = useUserPersonalizationContextSelector(
    (v) => ({
      updateLocation: v.updateLocation,
      userType: v.userType,
    }),
  );
  const [isLoading, setIsLoading] = useState(false);
  const [cartId, setCartId] = useState<string>('');
  const [currentProductId, setCurrentProductId] = useState<number | undefined>(
    0,
  );

  const [email, setEmail] = useState('');
  const [isCartSummaryModalOpen, setIsCartSummaryModalOpen] = useState(false);

  const [isInstallConfirmOpen, setIsInstallConfirmOpen] = useState(false);
  const [isInstaller, setIsInstaller] = useState<boolean>(false);
  const [isHome, setIsHome] = useState<boolean>(false);

  const [isUpdatingRoadHazard, setIsUpdatingRoadHazard] =
    useState<boolean>(false);
  const [isRemovingRoadHazard, setIsRemovingRoadHazard] =
    useState<boolean>(false);
  const [isOpenRoadHazardDealModal, setIsOpenRoadHazardDealModal] =
    useState<boolean>(false);
  const [cartSummary, setCartSummary] = useState<SiteCartSummary | null>(null);
  // null means the previously selected one is no longer available https://simpletire.atlassian.net/browse/SSI-145
  const [cartAppointment] = useState<
    SiteCartAppointmentResponse | null | undefined
  >();
  const [cartShipping, setCartShipping] = useState<
    SiteCartShippingResponse | undefined
  >();
  const [triggerCartCleanup, setTriggerCartCleanup] = useState<TriggerType>({
    flag: -1,
    removeShippingOnly: false,
  });
  const [isCartSummaryLoading, setIsCartSummaryLoading] =
    useState<boolean>(false);

  const [currentOrderCancelled, setCurrentOrderCancelled] =
    useState<boolean>(false);
  const [isAllShopModalOpen, setIsAllShopModalOpen] = useState(false);
  const [paymentType, setPaymentType] = useState<PAYMENT_OPTIONS>(
    PAYMENT_OPTIONS.CREDIT,
  );
  const [lastPaymentTypeBeforePaypal, setLastPaymentTypeBeforePaypal] =
    useState(paymentType);

  const [
    isShowingRemoveInstallationAlert,
    setIsShowingRemoveInstallationAlert,
  ] = useState<boolean>(false);
  const [customerId, setCustomerId] = useState<string>('');
  const [isOpenTimeChangeModalOnCheckout, setIsOpenTimeChangeModalOnCheckout] =
    useState<boolean>(false);
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false);
  const [
    removeInstallerFromShippingPage,
    setShouldRemoveInstallerFromShipping,
  ] = useState<boolean>(false);
  const [shipToMeForm, setShipToMeForm] = useState<ShipToMeFormValues>();
  const [shopCardValues, setShopCardValues] =
    useState<ConfirmAppointmentProps>();
  const [hasErrorFindShop, setHasErrorFindShop] = useState<boolean>(false);

  const isProductAddedInCart =
    cartSummary?.siteProducts.every((item) => item.productId) ?? false;
  const [isRoadHazardChecked, setIsRoadHazardChecked] = useState<boolean>(
    isOTSDeployment() ? true : false,
  );

  const isRoadhazardNotApplicableForAllProducts =
    cartSummary?.siteProducts.every(
      (product) => product.roadHazardState === 2,
    ) ?? false;

  const pathname = usePathname();
  const searchParams = getSearchParams();
  const hasMobileInstallInSearchParams = searchParams?.get('hasMobileInstall');
  const activeShippingTypeInSearchParams =
    searchParams?.get('activeShippingType');
  const router = useRouter();
  const isCheckoutPage = isCheckoutPagePath(pathname);

  const cartIdFromWidget = searchParams?.get('cartId');

  const isSimpleShop = useGlobalsContextSelector(
    (v) => Number(v.isSimpleShop) === 1,
  );

  const { isComingFromWidget } = useWidgetSource();

  const isShippingPage =
    isCheckoutPage && ROUTE_MAP[ROUTES.CHECKOUT_SHIPPING] === pathname;

  const isShippingConfirmationPage =
    isCheckoutPage && ROUTE_MAP[ROUTES.SHIPPING_CONFIRMATION] === pathname;
  const isOrderConfirmationPage = pathname?.includes('order-confirmation');
  const isOtherCheckoutPage =
    isCheckoutPage && !isOrderConfirmationPage && !isShippingConfirmationPage;
  const displayRoadsideAssistance = !isShippingConfirmationPage;
  const displayShippingInfo = isCheckoutPage || isInstaller;
  const isRoadHazardCheckboxVisible =
    userType !== USER_TYPE.SPECIAL_ORDER &&
    (isOtherCheckoutPage || !!isRoadHazardChecked) &&
    !isRoadhazardNotApplicableForAllProducts;

  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] || cartIdFromWidget || null;
  const [hasDefaultSelectMobileInstall, setHasDefaultSelectMobileInstall] =
    useState(() => {
      if (
        hasMobileInstallInSearchParams &&
        activeShippingTypeInSearchParams === SHIPPINGSERIVCES.MOBILEINSTALL
      ) {
        return true;
      }
      return false;
    });
  const [hasMobileInstall, setHasMobileInstallState] = useState(() => {
    if (hasMobileInstallInSearchParams) {
      return true;
    }
    return false;
  });

  const [selectedTab, setSelectedTab] = useState<string>(INSTALL_TAB.LOCAL);
  const [showInstallerTabs, setShowInstallerTabs] = useState<boolean>(
    isSimpleShop ? false : true, // dont show tabs in simpleshop env
  );

  const setHasMobileInstall = useCallback(
    (value: boolean) => {
      if (hasMobileInstall === value) {
        return;
      } else {
        setHasMobileInstallState(value);
      }
    },
    [hasMobileInstall, setHasMobileInstallState],
  );

  useEffect(() => {
    const checkMobileInstall = async () => {
      if (isComingFromWidget && !cartSummary) {
        return;
      }
      const response = await apiGetSiteInstallers({
        query: {
          cartId,
          frontQuantity:
            cartSummary?.siteProducts.reduce(
              (prev, cur) => prev + cur.quantity,
              0,
            ) + '',
          itemId: cartSummary?.siteProducts[0]?.productId.toString() || '',
          limit: '4',
          mobileInstall: 'true',
          source: SiteInstallerSourceEnum.CHECKOUT,
          userZip: cartSummary?.zip ? cartSummary.zip : '',
        },
      });

      if (
        response.isSuccess &&
        response.data.verifiedCount &&
        response.data.verifiedCount > 0
      ) {
        setHasMobileInstall(true);
      } else {
        setHasMobileInstall(false);
      }
    };
    // No need to check for mobile install as we get the value from widget app config
    if (
      cartSummary &&
      cartSummary.siteProducts &&
      !isSimpleShop &&
      !isComingFromWidget
    ) {
      checkMobileInstall();
    }
    if (isComingFromWidget && cartSummary?.zip) {
      updateLocation({ userLocationZip: cartSummary.zip as string });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getSiteCartSummary =
    useCallback(async (): Promise<SiteCartSummary | void> => {
      if (!storedCartId) {
        return;
      }
      const localRetrieveQoute = lscache.get(
        LOCAL_STORAGE[localProperties.RETRIEVE_QUOTE],
      );
      // OPT-2562. only skip cart summary API call if VWO is enabled and VWO cookie is missing. This should fix an issue that Cart becomes empty after refreshing on PDP and Catalog of OTS or navigating to Home page of OTS.
      if (
        window._vwo_code &&
        typeof cookies[COOKIES.VWO] === 'undefined' &&
        !localRetrieveQoute
      ) {
        return;
      }

      const isFromLoginLogout = cookies[SSO_COOKIE_CONSTANTS.SSO_EVENT_UPDATE];

      const widgetSourceId = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE_ID],
      );

      const searchParams = getSearchParams();
      const widgetSource = searchParams?.get('widgetSource');
      const subSource = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.SUB_SOURCE],
      );

      const input = {
        ...(widgetSource ? { widgetSource } : {}),
        ...(widgetSourceId ? { widgetSourceId } : {}),
      };

      const query = {
        id: storedCartId,
        vwo_user: cookies[COOKIES.VWO],
        ...input,
        ...(subSource ? { subSource } : {}),
      };

      setCartId(storedCartId);
      if (isFromLoginLogout) {
        setCookie(
          null,
          SSO_COOKIE_CONSTANTS.SSO_EVENT_UPDATE,
          '',
          cookieConfig,
        );
        setCookie(
          null,
          SSO_COOKIE_CONSTANTS.SSO_EVENT_UPDATE,
          '',
          cookieConfigKings,
        );
        const response = await apiUpdateSiteCartSummary({
          query,
          input,
          includeUserRegion: true,
          includeUserZip: true,
        });
        if (response.isSuccess) {
          setCartSummary(response.data.siteCart);
          setIsLoading(false);
          return response.data.siteCart;
        } else {
          setIsLoading(false);
          if (response.error.code === 'AbortError') {
            return;
          }
          if (response.error.statusCode === 404) {
            setTriggerCartCleanup((prev) => ({
              flag: prev.flag + 1,
              removeShippingOnly: false,
            }));
          }
        }
      } else {
        const response = await apiGetSiteCartSummary({
          query: {
            id: storedCartId,
            vwo_user: cookies[COOKIES.VWO],
            ...(widgetSource ? { widgetSource } : {}),
            ...(widgetSourceId ? { widgetSourceId } : {}),
          },
          includeUserRegion: true,
          includeUserZip: true,
        });
        if (response.isSuccess) {
          setCartSummary(response.data.siteCart);
          setIsLoading(false);
          return response.data.siteCart;
        } else {
          setIsLoading(false);
          if (response.error.code === 'AbortError') {
            return;
          }
          if (response.error.statusCode === 404) {
            setTriggerCartCleanup((prev) => ({
              flag: prev.flag + 1,
              removeShippingOnly: false,
            }));
          }
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [storedCartId]);

  const updateAndFetchCartSummary = useCallback(async () => {
    if (!storedCartId) {
      return;
    }
    const searchParams = getSearchParams();
    const widgetSource = searchParams?.get('widgetSource');

    if (widgetSource === 'pirelli') {
      return;
    }

    setCartId(storedCartId);

    const widgetSourceId = seStorage.getItem(
      SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE_ID],
    );
    const subSource = seStorage.getItem(SESSION_STORAGE[PROPERTIES.SUB_SOURCE]);

    const input = {
      ...(widgetSource ? { widgetSource } : {}),
      ...(widgetSourceId ? { widgetSourceId } : {}),
    };

    const query = {
      id: storedCartId,
      vwo_user: cookies[COOKIES.VWO],
      ...input,
      ...(subSource ? { subSource } : {}),
    };
    const response = await apiUpdateSiteCartSummary({
      query,
      input,
      includeUserRegion: true,
      includeUserZip: true,
    });

    if (response.isSuccess) {
      setCartSummary(response.data.siteCart);
    } else {
      if (response.error.code === 'AbortError') {
        return;
      }
      if (response.error.statusCode === 404) {
        setTriggerCartCleanup((prev) => ({
          flag: prev.flag + 1,
          removeShippingOnly: false,
        }));
      } else {
        throw new Error(response.error.message);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storedCartId]);

  useEffectOnlyOnce(
    () => {
      if (!storedCartId) {
        clearCartCookies();
        return;
      }

      (async () => {
        const siteCart = await getSiteCartSummary();
        if (siteCart) {
          // STHD-8456. when a user select Katapult, then refresh page and place order with CC, he isn't charged with sales tax because paymentType is reset to CC, but not call updateCartSummary API to update paymentType on server side.
          // This run only once effect will update paymentType to CC after refreshing page to avoid this type of issue. it also update itemId which was done in `getIsShippingDataSet` function to update cart summary for promotion apply or remove from cart
          await updateCartSummary({
            itemId: siteCart?.siteProducts[0]?.productId.toString(),
            paymentType: PAYMENT_OPTIONS.CREDIT,
          });
        }
      })();
    },
    {},
    () => true,
  );

  useEffect(() => {
    if (
      hasMobileInstallInSearchParams &&
      activeShippingTypeInSearchParams === SHIPPINGSERIVCES.MOBILEINSTALL
    ) {
      setHasDefaultSelectMobileInstall(true);
    }
  }, [hasMobileInstallInSearchParams, activeShippingTypeInSearchParams]);

  const createCartSummary = useCallback(
    async (
      siteCartSummaryRequest: SiteCartSummaryRequest,
      signal?: AbortSignal,
    ) => {
      const widgetSourceId = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE_ID],
      );

      if (widgetSourceId) {
        siteCartSummaryRequest.widgetSourceId = widgetSourceId;
      }
      const searchParams = getSearchParams();
      const widgetSource = searchParams?.get('widgetSource');
      const subSource = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.SUB_SOURCE],
      );
      if (widgetSource) {
        siteCartSummaryRequest.widgetSource = widgetSource;
      }
      if (subSource) {
        siteCartSummaryRequest.subSource = subSource;
      }

      const response = await apiCreateSiteCartSummary({
        includeUserRegion: true,
        includeUserZip: true,
        input: siteCartSummaryRequest,
        query: {
          vwo_user: cookies[COOKIES.VWO],
        },
        signal,
      });

      if (response.isSuccess) {
        setCookie(
          null,
          COOKIES.CART_ID,
          response.data.siteCart.id.toString(),
          cookieConfig,
        );
        if (isOTSDeployment()) {
          setCookie(
            null,
            COOKIES.CART_ID,
            response.data.siteCart.id.toString(),
            cookieConfigKings,
          );
        }

        setCartId(response.data.siteCart.id.toString());
        setCartSummary(response.data.siteCart);
      }
      return response;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const updateCartSummary = useCallback(
    async (
      siteCartSummaryRequest: SiteCartSummaryRequest,
      signal?: AbortSignal,
      forceCall?: boolean,
    ): Promise<AsyncResponse<SiteCartSummaryResponse> | void> => {
      const searchParams = getSearchParams();
      const widgetSource = searchParams?.get('widgetSource');
      if (widgetSource === 'pirelli' && forceCall !== true) {
        return;
      }

      if (!cartId && !storedCartId) {
        logger.error('There is no cart yet!');
        return;
      }

      const stableCartId = cartId || storedCartId || '';

      setIsCartSummaryLoading(true);
      const widgetSourceId = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE_ID],
      );
      const subSource = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.SUB_SOURCE],
      );

      if (widgetSourceId) {
        siteCartSummaryRequest.widgetSourceId = widgetSourceId;
      }

      if (widgetSourceId) {
        siteCartSummaryRequest.widgetSource = widgetSource;
      }

      if (subSource) {
        siteCartSummaryRequest.subSource = subSource;
      }

      // OPT-2253. don't send http put API call of cart summary if storedCartId is not available. it will avoid random 404 error on multiple pages when running Lighthouse test.
      if (!storedCartId) {
        return;
      }
      const response = await apiUpdateSiteCartSummary({
        includeUserRegion: true,
        includeUserZip: true,
        input: siteCartSummaryRequest,
        query: {
          id: storedCartId ? storedCartId : '',
          vwo_user: cookies[COOKIES.VWO],
        },
        signal,
      });
      if (response.isSuccess) {
        setIsCartSummaryLoading(false);
        setCartSummary(response.data.siteCart);
        if (
          siteCartSummaryRequest.isNewItem &&
          siteCartSummaryRequest.itemId &&
          siteCartSummaryRequest.itemQuantity &&
          siteCartSummaryRequest.itemQuantity > 0
        ) {
          const productId = siteCartSummaryRequest.itemId;
          const { mobileInstallerList, installerList } =
            await getSiteInstallersForMobileOptions(
              response.data.siteCart,
              cartId,
            );
          response.data.siteCart.siteProducts.map((product) => {
            if (product.productId.toString() == productId && product?.price) {
              let products: ExtendedEventProperties = mapToProductData(
                product,
                stableCartId,
                siteCartSummaryRequest.itemQuantity || 0,
              );
              products = {
                ...products,
                installerList,
                mobileInstallerList,
                zip: response.data.siteCart.zip ?? '',
              };
              rudderstackSendTrackEvent(
                RudderstackTrackEventName.ADD_TO_CART,
                products,
              );
            }
          });
          GA.addToDataLayer({
            cartAddInstallerId: siteCartSummaryRequest.installerId || undefined,
            cartAddIsRoadHazard:
              siteCartSummaryRequest?.isGroupRoadHazard?.toString() ||
              undefined,
            cartAddItemId: siteCartSummaryRequest.itemId,
            cartAddItemQuantity: siteCartSummaryRequest.itemQuantity,
            event: 'isCartAdd',
          });
        }
        if (
          siteCartSummaryRequest.promoCode &&
          response.data.siteCart.siteCartCoupons &&
          response.data.siteCart.siteCartCoupons.length > 0
        ) {
          response.data.siteCart.siteCartCoupons.forEach((coupon) => {
            if (!coupon.errorDescription) {
              rudderstackSendTrackEvent(
                RudderstackTrackEventName.COUPON_APPLIED,
                {
                  cart_id: stableCartId,
                  coupon_id: coupon?.couponId ?? '',
                  coupon_name: coupon.promoCode,
                  discount: coupon.discountInCents ?? '',
                },
              );
            } else {
              rudderstackSendTrackEvent(
                RudderstackTrackEventName.COUPON_DENIED,
                {
                  cart_id: stableCartId,
                  coupon_id: coupon?.couponId ?? '',
                  coupon_name: coupon.promoCode,
                  reason: coupon?.errorDescription ?? '',
                },
              );
            }
          });
        }

        return response;
      } else {
        if (response.error.statusCode === 404) {
          setTriggerCartCleanup((prev) => ({
            flag: prev.flag + 1,
            removeShippingOnly: false,
          }));
          if (siteCartSummaryRequest.itemId) {
            await createCartSummary({
              email: siteCartSummaryRequest.email,
              excludeShipping: false,
              idMeVerifiedStatus: IDMEVerifiedStatus.NULL,
              installerId: siteCartSummaryRequest.installerId || null,
              isNewItem: true,
              isRoadHazard:
                siteCartSummaryRequest.isGroupRoadHazard ??
                siteCartSummaryRequest.isRoadHazard,
              itemId: siteCartSummaryRequest.itemId,
              itemQuantity: siteCartSummaryRequest.itemQuantity,
              promoCode: null,
              vehicleMake: siteCartSummaryRequest?.vehicleMake,
              vehicleModel: siteCartSummaryRequest?.vehicleModel,
              vehicleTrim: siteCartSummaryRequest?.vehicleTrim,
              vehicleYear: siteCartSummaryRequest?.vehicleYear,
            });
          }
          setIsCartSummaryLoading(false);
        } else {
          setIsCartSummaryLoading(false);
          return;
        }
        return;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [cartId, createCartSummary, storedCartId],
  );

  async function getSiteInstallersForMobileOptions(
    cartSummary: SiteCartSummary | null,
    cartId: string,
  ): Promise<{
    installerList: SiteInstallerItem[];
    mobileInstallerList: SiteInstallerItem[];
  }> {
    const frontQuantity =
      cartSummary?.siteProducts.reduce((prev, cur) => {
        return prev + cur.quantity;
      }, 0) + '';

    const itemId = cartSummary?.siteProducts[0].productId.toString() || '';
    const userZip = cartSummary?.zip ? cartSummary?.zip : '';

    const [mobileInstallers, installers] = await Promise.all([
      apiGetSiteInstallers({
        query: {
          cartId,
          frontQuantity,
          itemId,
          mobileInstall: 'true',
          source: SiteInstallerSourceEnum.CHECKOUT,
          userZip,
        },
      }),
      apiGetSiteInstallers({
        query: {
          cartId,
          frontQuantity,
          itemId,
          mobileInstall: 'false',
          source: SiteInstallerSourceEnum.CHECKOUT,
          userZip,
        },
      }),
    ]);

    const mobileInstallerList = mobileInstallers.isSuccess
      ? mobileInstallers.data.siteInstallers.siteInstallerList
      : [];
    const installerList = installers.isSuccess
      ? installers.data.siteInstallers.siteInstallerList
      : [];

    return { mobileInstallerList, installerList };
  }

  const updateTireQuantity = useCallback(
    async ({
      productId,
      quantity,
    }: {
      productId: number;
      quantity: number;
    }) => {
      await updateCartSummary(
        {
          itemId: productId.toString(),
          itemQuantity: quantity,
        },
        undefined,
        true,
      );
    },
    [updateCartSummary],
  );

  const updateRoadHazard = useCallback(
    async (value?: boolean) => {
      setIsLoading(true);
      setIsUpdatingRoadHazard(true);
      try {
        if (value) {
          GA.addToDataLayer({
            event: 'isCheckoutStep',
            stepName: 'ChooseRoadHazard',

            shipToOption: '',
            shipToRefId: '',
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              step: 'ChooseRoadHazard',
            },
          );
        } else {
          GA.addToDataLayer({
            event: 'isCheckoutStep',
            stepName: 'RemoveRoadHazard',

            shipToOption: '',
            shipToRefId: '',
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
            {
              checkout_id: cartSummary?.cartUuid ?? '',
              step: 'RemoveRoadHazard',
            },
          );
          setIsRemovingRoadHazard(true);
        }
        if (currentProductId) {
          await updateCartSummary({
            itemId: currentProductId?.toString(),
            isRoadHazard: value,
          });
          setIsRoadHazardChecked(!!value);
          setIsLoading(false);
        } else {
          await updateCartSummary({
            isGroupRoadHazard: value,
          });
          setIsRoadHazardChecked(!!value);
          setIsLoading(false);
        }
      } finally {
        setIsUpdatingRoadHazard(false);
        setIsLoading(false);
        setIsRemovingRoadHazard(false);
        setIsRoadHazardChecked(!!value);
      }
    },
    [cartSummary?.cartUuid, currentProductId, updateCartSummary],
  );

  const updateIDMeVerifyStatus = useCallback(
    async (status: IDMEVerifiedStatus) => {
      return await updateCartSummary({
        idMeVerifiedStatus: status,
      });
    },
    [updateCartSummary],
  );

  const applyPromoCode = useCallback(
    async (couponCode: string) => {
      rudderstackSendTrackEvent(RudderstackTrackEventName.COUPON_ENTERED, {
        coupon_name: couponCode,
        cartId,
      });
      await updateCartSummary({
        promoCode: couponCode,
        removePromoCode: null,
      });
    },
    [cartId, updateCartSummary],
  );

  const removePromoCode = useCallback(
    async (couponCode: string, coupon?: SiteCartCouponItem) => {
      rudderstackSendTrackEvent(RudderstackTrackEventName.COUPON_REMOVED, {
        coupon_name: couponCode,
        coupon_id: coupon?.couponId ?? '',
        cart_id: cartId,
        discount: coupon?.discountInCents ?? '',
      });
      await updateCartSummary({
        promoCode: null,
        removePromoCode: couponCode,
        excludeShipping: !isCheckoutPage ? true : undefined,
      });
    },
    [cartId, updateCartSummary, isCheckoutPage],
  );

  const handleCheckout = useCallback(
    async (abt50ExperimentNumber: number) => {
      if (!cartSummary) {
        return;
      }
      const checkoutServicesPage =
        abt50ExperimentNumber === 0
          ? ROUTE_MAP[ROUTES.CHECKOUT_SERVICES]
          : ROUTE_MAP[ROUTES.CHECKOUT_SERVICES] + '?showExitAttentive=true';
      const checkoutPaymentsPage =
        abt50ExperimentNumber === 0
          ? ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]
          : ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT] + '?showExitAttentive=true';
      const checkoutShippingPage =
        abt50ExperimentNumber === 0
          ? ROUTE_MAP[ROUTES.CHECKOUT_SHIPPING]
          : ROUTE_MAP[ROUTES.CHECKOUT_SHIPPING] + '?showExitAttentive=true';
      const checkoutShippingConfirmationPage =
        abt50ExperimentNumber === 0
          ? ROUTE_MAP[ROUTES.SHIPPING_CONFIRMATION]
          : ROUTE_MAP[ROUTES.SHIPPING_CONFIRMATION] + '?showExitAttentive=true';
      const product = cartSummary?.siteProducts?.[0];
      const isRoadhazardNotApplicableForAllProducts =
        checkIfRoadHazardIsApplicable(cartSummary) ||
        userType !== USER_TYPE.SPECIAL_ORDER;
      const redirectPath = isRoadhazardNotApplicableForAllProducts
        ? checkoutPaymentsPage
        : checkoutServicesPage;
      // redirect directly to payments if the tires in cart are not applicable for roadhazard

      if (!product) {
        return;
      }

      // updatePagePathPriorToCheckout(currentPageFullPath);
      if (cartSummary.installerDetails) {
        if (cartSummary.installerDetails.website === 'fedex.com') {
          GA.addToDataLayer({
            event: 'isCheckoutStep',
            stepName: 'ChooseShipping',
            // eslint-disable-next-line sort-keys
            shipToOption: 'ShipPickup',
            shipToRefId: cartShipping?.cartShipping.id,
            // eslint-disable-next-line sort-keys
            cartSummary,
          });

          router.push(redirectPath);
        } else {
          GA.addToDataLayer({
            event: 'isCheckoutStep',
            stepName: 'ChooseShipping',
            // eslint-disable-next-line sort-keys
            shipToOption: 'ShipInstaller',
            shipToRefId: cartSummary.installerDetails.installerId,
            // eslint-disable-next-line sort-keys
            cartSummary,
          });
          if (cartAppointment) {
            router.push(checkoutServicesPage);
          } else {
            router.push(checkoutShippingPage);
          }
        }
      } else {
        const isAllInstallable = cartSummary.siteProducts.every(
          (product) => !!product.isInstallable,
        );
        if (!isAllInstallable) {
          GA.addToDataLayer({
            event: 'isCheckoutStep',
            stepName: 'StartCheckout ',
            // eslint-disable-next-line sort-keys
            shipToOption: 'ShipHome',
            shipToRefId: '',
            // eslint-disable-next-line sort-keys
            cartSummary,
          });
          router.push(checkoutShippingPage);
          return;
        }

        if (cartShipping) {
          const hasInstaller =
            cartShipping?.shippingOption === ShippingType.INSTALLER;

          if (hasInstaller && cartAppointment) {
            GA.addToDataLayer({
              event: 'isCheckoutStep',
              stepName: 'ChooseShipping',
              // eslint-disable-next-line sort-keys
              shipToOption: 'ShipInstaller',
              shipToRefId: cartShipping.cartShipping.installer?.installerId,
              // eslint-disable-next-line sort-keys
              cartSummary,
            });

            router.push(checkoutShippingConfirmationPage);
          } else {
            router.push(checkoutShippingPage);
          }
        } else {
          GA.addToDataLayer({
            cartSummary,
            event: 'isCheckoutStep',
            shipToOption: 'ShipHome',
            shipToRefId: '',
            stepName: 'StartCheckout ',
          });
          router.push(checkoutShippingPage);
        }
      }
    },
    [
      cartAppointment,
      cartShipping,
      cartSummary,
      // currentPageFullPath,
      router,
      userType,
    ],
  );

  return {
    applyPromoCode,
    cartId,
    cartShipping,
    cartSummary,
    createCartSummary,
    currentOrderCancelled,
    customerId,
    displayRoadsideAssistance,
    displayShippingInfo,
    email,
    getSiteCartSummary,
    getSiteInstallersForMobileOptions,
    handleCheckout,
    hasDefaultSelectMobileInstall,
    hasErrorFindShop,
    hasMobileInstall,
    isAllShopModalOpen,
    isAppointmentModalOpen,
    isCartSummaryLoading,
    isCartSummaryModalOpen,
    isHome,
    isInstallConfirmOpen,
    isInstaller,
    isLoading,
    isOpenRoadHazardDealModal,
    isOpenTimeChangeModalOnCheckout,
    isProductAddedInCart,
    isRemovingRoadHazard,
    isRoadHazardCheckboxVisible,
    isRoadHazardChecked,
    isShippingPage,
    isShowingRemoveInstallationAlert,
    isUpdatingRoadHazard,
    lastPaymentTypeBeforePaypal,
    paymentType,
    removeInstallerFromShippingPage,
    removePromoCode,
    selectedTab,
    setCartId,
    setCartShipping,
    setCartSummary,
    setCurrentOrderCancelled,
    setCurrentProductId,
    setCustomerId,
    setEmail,
    setHasDefaultSelectMobileInstall,
    setHasErrorFindShop,
    setHasMobileInstall,
    setIsAllShopModalOpen,
    setIsAppointmentModalOpen,
    setIsCartSummaryModalOpen,
    setIsHome,
    setIsInstallConfirmOpen,
    setIsInstaller,
    setIsLoading,
    setIsOpenRoadHazardDealModal,
    setIsOpenTimeChangeModalOnCheckout,
    setIsRoadHazardChecked,
    setIsShowingRemoveInstallationAlert,
    setLastPaymentTypeBeforePaypal,
    setPaymentType,
    setSelectedTab,
    setShipToMeForm,
    setShopCardValues,
    setShouldRemoveInstallerFromShipping,
    setShowInstallerTabs,
    setTriggerCartCleanup,
    shipToMeForm,
    shopCardValues,
    showInstallerTabs,
    triggerCartCleanup,
    updateAndFetchCartSummary,
    updateCartSummary,
    updateIDMeVerifyStatus,
    updateRoadHazard,
    updateTireQuantity,
  };
}

function CartSummaryContextSetWidgetIdToSessionStorage() {
  useEffect(() => {
    const searchParams = getSearchParams();
    const widgetSourceId = searchParams?.get('widgetSourceId');
    if (widgetSourceId) {
      seStorage.setItem(
        SESSION_STORAGE[PROPERTIES.WIDGET_SOURCE_ID],
        widgetSourceId,
      );
    }
  }, []);

  return null;
}

function CartSummaryModalVisibilityEffect() {
  const setIsCartSummaryModalOpen = useCartSummaryContextSelector(
    (v) => v.setIsCartSummaryModalOpen,
  );

  useEffect(() => {
    const handleModalVisibility = (isVisible: boolean) => {
      setIsCartSummaryModalOpen(isVisible);
    };

    eventEmitters.setCartSummaryModalVisibility.on(handleModalVisibility);

    return () => {
      eventEmitters.setCartSummaryModalVisibility.off(handleModalVisibility);
    };
  }, [setIsCartSummaryModalOpen]);

  return null;
}

function CartSummaryRoadHazardCheckEffect() {
  const {
    isRoadHazardChecked,
    updateRoadHazard,
    cartSummary,
    setIsRoadHazardChecked,
    updateCartSummary,
  } = useCartSummaryContextSelector((v) => ({
    cartSummary: v.cartSummary,
    isRoadHazardChecked: v.isRoadHazardChecked,
    setIsRoadHazardChecked: v.setIsRoadHazardChecked,
    updateCartSummary: v.updateCartSummary,
    updateRoadHazard: v.updateRoadHazard,
  }));

  const { isSourceInstallerWidget, isSourceAffiliateWidget } =
    useWidgetSource();

  const isLinkToTRCProm = useMemo(
    () =>
      cartSummary?.siteProducts
        .flatMap(
          (product) =>
            product.siteProductPromotion as unknown as PromoListItem[],
        )
        .filter(
          (promo) =>
            promo.linkToTRCProm &&
            promo.couponAmount == '100.00' &&
            promo.couponAmountType == 'Percentage' &&
            promo.alreadyAppliedIntoCart,
        ),
    [cartSummary?.siteProducts],
  );

  // Use a ref to store the current value of isRoadHazardChecked
  const isRoadHazardCheckedRef = useRef(isRoadHazardChecked);

  // Update the ref value when isRoadHazardChecked changes
  useEffect(() => {
    isRoadHazardCheckedRef.current = isRoadHazardChecked;
  }, [isRoadHazardChecked]);

  // First effect (fetchData)
  useEffect(() => {
    async function fetchData() {
      const widgetSourceType = loStorage.getItem(
        LOCAL_STORAGE[localProperties.WIDGET_SOURCE_TYPE],
      );
      if (
        widgetSourceType !== '1' &&
        isLinkToTRCProm?.length == 0 &&
        (isSourceInstallerWidget || isSourceAffiliateWidget)
      ) {
        loStorage.setItem(
          LOCAL_STORAGE[localProperties.WIDGET_SOURCE_TYPE],
          '1',
        );
        await updateRoadHazard(false);
      }
    }
    fetchData();
  }, [
    isLinkToTRCProm?.length,
    isSourceAffiliateWidget,
    isSourceInstallerWidget,
    updateRoadHazard,
  ]);

  // Second effect (Cart Summary Road Hazard check)
  useEffect(() => {
    if (!cartSummary) {
      return;
    } // Avoid running if cartSummary is undefined or null

    const roadHazardCost = cartSummary.roadHazardCostInCents;
    if (roadHazardCost > 0) {
      // Only update if it's not already true
      if (!isRoadHazardCheckedRef.current) {
        setIsRoadHazardChecked(true);
      }
    } else {
      // Set false only if it's not already false
      if (isRoadHazardCheckedRef.current) {
        setIsRoadHazardChecked(false);
      }
    }
  }, [
    cartSummary, // Ensure cartSummary is fully passed as a dependency
    setIsRoadHazardChecked,
    updateCartSummary,
  ]);

  return null;
}

function CartSummaryContextHashParamsEffect() {
  const pathname = usePathname();
  const isCheckoutPage = isCheckoutPagePath(pathname);
  const { isMobile, greaterThan } = useBreakpoints();
  const setIsCartSummaryModalOpen = useCartSummaryContextSelector(
    (v) => v.setIsCartSummaryModalOpen,
  );

  useEffect(() => {
    const searchParams = getSearchParams();
    const isSourcePirelli = searchParams?.get('widgetSource') === 'pirelli';
    if (!isSourcePirelli) {
      return;
    }
    const hashParams = window.location.hash.split('#').pop();
    if (!hashParams) {
      return;
    }
    const queries = new URLSearchParams(hashParams);

    if (queries.get('cart_summary_modal_open') !== 'true') {
      return;
    }

    setIsCartSummaryModalOpen(true);
  }, [pathname, greaterThan.M, isCheckoutPage, setIsCartSummaryModalOpen]);

  const localRetrieveQoute = lscache.get(
    LOCAL_STORAGE[localProperties.RETRIEVE_QUOTE],
  );

  useEffect(() => {
    const searchParams = getSearchParams();
    const isSourcePirelli = searchParams?.get('widgetSource') === 'pirelli';
    if (isMobile && localRetrieveQoute == 'yes' && !isSourcePirelli) {
      setIsCartSummaryModalOpen(true);
    }
  }, [isMobile, localRetrieveQoute, setIsCartSummaryModalOpen]);

  return null;
}

function CartSummaryContextRudderstackEffect() {
  const { isCartSummaryModalOpen, cartSummary } = useCartSummaryContextSelector(
    (v) => ({
      isCartSummaryModalOpen: v.isCartSummaryModalOpen,
      cartSummary: v.cartSummary,
    }),
  );
  const isSimpleShop = isSimpleShopDeployment();

  useEffect(() => {
    if (isCartSummaryModalOpen) {
      const cookies = parseCookies();
      const storedCartId = cookies[COOKIES.CART_ID] || null;
      if (storedCartId && cartSummary) {
        rudderstackSendTrackEvent(RudderstackTrackEventName.CART_VIEWED, {
          cart_id: storedCartId,
          currency: 'USD',
          products: mapToCartObject(cartSummary) ?? '',
          ...(isSimpleShop && { step_name: 'Schedule_installation' }),
        });
      }
    }
  }, [isCartSummaryModalOpen, cartSummary, isSimpleShop]);

  return null;
}

function CartSummaryContextSetResolvePaymentTypeEffect() {
  const setPaymentType = useCartSummaryContextSelector((v) => v.setPaymentType);

  useEffect(() => {
    const searchParams = getSearchParams();
    const chargeId = searchParams?.get('charge_id');
    if (chargeId && chargeId.length !== 0) {
      setPaymentType(PAYMENT_OPTIONS.RESOLVE);
    }
  }, [setPaymentType]);

  return null;
}

function CartSummaryContextSetAffirmPaymentTypeEffect() {
  const setPaymentType = useCartSummaryContextSelector((v) => v.setPaymentType);

  /**
   * @TODO  this part is overlapping with Shipping.context.tsx. need to refactor
   * */
  useEffect(() => {
    const hasError = window.location.hash.includes('#affirm-error');
    if (hasError) {
      setPaymentType(PAYMENT_OPTIONS.AFFIRM);
    }
  }, [setPaymentType]);

  return null;
}

const CartSummaryContextEffect = memo(() => {
  return (
    <>
      <CartSummaryContextSetWidgetIdToSessionStorage />
      <CartSummaryModalVisibilityEffect />
      <CartSummaryContextHashParamsEffect />
      <CartSummaryContextRudderstackEffect />
      <CartSummaryContextSetResolvePaymentTypeEffect />
      <CartSummaryContextSetAffirmPaymentTypeEffect />
      <CartSummaryRoadHazardCheckEffect />
    </>
  );
});

export function CartSummaryContextProvider({ children }: ContextProps) {
  const value = useContextSetup();

  return (
    <CartSummaryContext.Provider value={value}>
      <CartSummaryContextEffect />
      {children}
    </CartSummaryContext.Provider>
  );
}

export const useCartSummaryContextSelector = <SelectedValue,>(
  selector: Selector<CartSummaryContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<CartSummaryContextProps, SelectedValue>(
    CartSummaryContext,
    selector,
    equalCompareFn,
  );
