import { SiteGlobals } from '~/data/models/SiteGlobals';
import { SiteMenuContextProps } from '~/data/models/SiteMenu';
import { SiteNotificationList } from '~/data/models/SiteNotificationsList';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetSiteGlobals(
  accountType?: string,
  extraQueryParams?: Record<string, string>,
) {
  const response = await fetchWithErrorHandling<{
    siteGlobals: SiteGlobals;
  }>({
    endpoint: '/v1/site/globals',
    extraQueryParams,
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    method: 'get',
    query: {
      uniqueIdentifier: accountType || '',
    },
  });

  return response;
}

export async function backendGetSiteGlobalsWithDefaultUserZipAndRegion(
  extraQueryParams?: Record<string, string>,
) {
  const defaultUserZip = '19053';
  const defaultUserRegion = '2';
  const response = await fetchWithErrorHandling<{
    siteGlobals: SiteGlobals;
  }>({
    endpoint: '/v1/site/globals',
    extraQueryParams,
    includeAuthorization: true,
    includeUserSSOUid: true,
    method: 'get',
    query: {
      userZip: defaultUserZip,
      userRegion: defaultUserRegion,
    },
  });

  return response;
}

export async function backendGetSiteNotifications() {
  const response = await fetchWithErrorHandling<SiteNotificationList>({
    endpoint: '/v1/site/notifications',
    includeAuthorization: true,
    method: 'get',
  });

  return response;
}

export async function backendGetSiteMenu(
  {
    channel,
  }: {
    channel?: string;
  },
  extraQueryParams?: Record<string, string>,
) {
  const response = await fetchWithErrorHandling<SiteMenuContextProps>({
    endpoint: '/v1/site/menu',
    extraQueryParams,
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    method: 'get',
    query: {
      channel: channel ? channel : '',
    },
  });

  return response;
}

export async function backendGetSitePromotionName(promotionId: number) {
  const response = await fetchWithErrorHandling<{
    promotionId: string;
    promotionName: string;
  }>({
    endpoint: `/v1/site/promotion/${promotionId}`,
    includeAuthorization: true,
    method: 'get',
  });
  return response;
}
