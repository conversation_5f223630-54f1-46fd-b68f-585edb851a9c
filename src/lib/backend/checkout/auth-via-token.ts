import {
  AuthViaTokenRequest,
  AuthViaTokenResponse,
} from '~/data/models/AuthViaToken';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendPostAuthViaClientToken(
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<AuthViaTokenResponse>({
    endpoint: '/v2/site/payment/authvia/customer',
    extraQueryParams,
    includeAuthorization: true,
    method: 'post',
  });
}

export async function backendPutAuthViaClientToken(
  {
    input,
  }: {
    input: AuthViaTokenRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    AuthViaTokenResponse,
    AuthViaTokenRequest
  >({
    endpoint: '/v2/site/payment/authvia/customer',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
  });
}
