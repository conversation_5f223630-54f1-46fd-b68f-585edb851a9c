import { cookies, headers } from 'next/headers';

import { ssoUserIdFunction } from '../backend/bootstrap';
import { COOKIES } from '../constants/cookies';
import { AAFES_COOKIES } from '../utils/aafes/aafes.constants';
import { isOTSDeployment } from '../utils/deploy';

const isOTS = isOTSDeployment();

export async function generateExtraQueryParams({
  includeUserSSOUid,
  includeUserTime,
  includeUserVwo,
  includeUserRegion,
  includeUserZip,
  query,
}: {
  includeUserRegion?: boolean;
  includeUserSSOUid?: boolean;
  includeUserTime?: boolean;
  includeUserVwo?: boolean;
  includeUserZip?: boolean;
  jsonBody?: Record<string, string>;
  query?: Record<string, string>;
}) {
  const extraQueryParams: Record<string, string> = {};
  const [headersList, cookieStore] = await Promise.all([headers(), cookies()]);

  const xUserSessionId = headersList.get('x-user-session-id');
  const xUserTime = headersList.get('x-user-time');

  const simpleShopId = cookieStore.get(COOKIES.SIMPLE_SHOP_ID)?.value;
  const otsAccountTypeCompany = cookieStore.get(
    COOKIES.OTS_ACCOUNT_TYPE_COMPANY,
  )?.value;
  const otsWidgetId = cookieStore.get(COOKIES.OTS_WIDGET_ID)?.value;
  const widgetSourceId = cookieStore.get(COOKIES.WIDGET_SOURCE_ID)?.value;
  const aafesCID = cookieStore.get(AAFES_COOKIES.AAFES_CUSTOMER_ID)?.value;
  const region = cookieStore.get(COOKIES.REGION)?.value ?? '';
  const zip = cookieStore.get(COOKIES.ZIP)?.value ?? '';

  if (includeUserZip) {
    if (query?.userZip) {
      extraQueryParams.userZip = query.userZip;
    }
    if (zip) {
      extraQueryParams.userZip = zip;
    }
  }
  if (includeUserRegion) {
    if (query?.userRegion) {
      extraQueryParams.userRegion = query.userRegion;
    }
    if (region) {
      extraQueryParams.userRegion = region;
    }
  }
  if (includeUserSSOUid) {
    const ssoUid = query?.ssoUid ?? (await ssoUserIdFunction(cookieStore));

    if (ssoUid) {
      extraQueryParams.ssoUid = ssoUid;
    }
  }
  if (xUserSessionId) {
    extraQueryParams.xUserSessionId = xUserSessionId;
  }
  if (includeUserTime) {
    if (xUserTime) {
      extraQueryParams.xUserTime = xUserTime;
    }
  }
  if (includeUserVwo && !otsAccountTypeCompany) {
    const xVWOUser =
      headersList.get('x-vwo-user') ?? cookieStore.get(COOKIES.VWO)?.value;
    if (xVWOUser) {
      extraQueryParams.xVWOUser = xVWOUser;
    }
  }
  if (simpleShopId) {
    extraQueryParams.simpleShopId = simpleShopId;
  }
  if (otsAccountTypeCompany) {
    extraQueryParams.otsAccountTypeCompany = otsAccountTypeCompany;
  }
  if (otsWidgetId) {
    extraQueryParams.otsWidgetId = otsWidgetId;
  }
  if (widgetSourceId && !isOTS) {
    extraQueryParams.widgetSourceId = widgetSourceId;
  }
  if (aafesCID) {
    extraQueryParams.aafesCID = aafesCID;
  }

  return extraQueryParams;
}
